# Условная авторизация (Conditional Authentication)

Этот документ описывает реализацию условной авторизации в API, которая позволяет включать и выключать требования авторизации через переменную окружения `REQUIRE_AUTH`.

## Обзор

Система поддерживает три типа эндпоинтов:

1. **Условная авторизация** - эндпоинты, которые уважают настройку `REQUIRE_AUTH`
2. **Без авторизации** - эндпоинты, которые всегда доступны без авторизации
3. **Обязательная авторизация** - эндпоинты, которые всегда требуют авторизацию

## Конфигурация

### Переменная окружения

```bash
# В файле .env
REQUIRE_AUTH=true   # Включить авторизацию (по умолчанию)
REQUIRE_AUTH=false  # Отключить авторизацию
```

### Поведение

- **REQUIRE_AUTH=true**: Условные эндпоинты требуют валидный API ключ
- **REQUIRE_AUTH=false**: Условные эндпоинты работают без авторизации (анонимный пользователь с полными правами)

## Категории эндпоинтов

### 1. Условная авторизация (уважают REQUIRE_AUTH)

Эти эндпоинты используют условные зависимости (`require_*_conditional`):

#### REST API
- `POST /api/subtitles/get_id` - получение ID видео
- `POST /api/subtitles/get_id_async` - получение ID видео (асинхронно)
- `POST /api/subtitles` - извлечение субтитров
- `POST /api/summarize` - суммаризация текста
- `POST /api/summarize/file` - суммаризация файла
- `GET /api/task/{task_id}` - получение статуса задачи
- `GET /api/alerts` - получение алертов
- `GET /api/logs` - получение логов
- `GET /api/logs/stats` - статистика логов
- `POST /api/alerts/{alert_name}/acknowledge` - подтверждение алерта
- `POST /api/convert/ttml-to-txt` - конвертация TTML в TXT

#### WebSocket
- `WS /ws/subtitles` - WebSocket для субтитров
- `WS /ws/summarize` - WebSocket для суммаризации

### 2. Без авторизации (всегда доступны)

Эти эндпоинты никогда не требуют авторизацию:

- `POST /ping` - проверка доступности
- `GET /health` - проверка здоровья системы
- `GET /metrics` - метрики приложения

### 3. Обязательная авторизация (всегда требуют авторизацию)

Эти эндпоинты всегда требуют валидный API ключ:

- `GET /api/auth/keys` - управление API ключами
- `POST /api/auth/keys` - создание API ключей
- `DELETE /api/auth/keys/{key}` - удаление API ключей
- `GET /api/auth/me` - информация о текущем пользователе
- `GET /api/auth/permissions` - доступные разрешения

## Техническая реализация

### Новые функции аутентификации

```python
# В api/middleware/auth.py

async def get_current_user_conditional(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> dict[str, Any] | None:
    """
    Условная аутентификация - возвращает анонимного пользователя 
    если REQUIRE_AUTH=false, иначе требует валидную аутентификацию.
    """

def require_permissions_conditional(*required_permissions: str):
    """
    Условная проверка разрешений - уважает настройку REQUIRE_AUTH.
    """

# Условные зависимости
require_read_conditional = require_permissions_conditional("read")
require_write_conditional = require_permissions_conditional("write")
require_admin_conditional = require_permissions_conditional("admin")
```

### Анонимный пользователь

Когда `REQUIRE_AUTH=false`, система создает анонимного пользователя:

```python
{
    "name": "anonymous",
    "permissions": ["read", "write", "admin"],  # Полные права
    "rate_limit": 1000,
    "auth_method": "disabled",
}
```

### WebSocket аутентификация

WebSocket эндпоинты уже поддерживали `REQUIRE_AUTH` через функцию `get_websocket_user()`, которая проверяет настройку и возвращает `None` если авторизация отключена.

## Миграция с предыдущей версии

### Удаленные настройки

- `DISABLE_AUTH_FOR_TTML_CONVERSION` - заменена на общую `REQUIRE_AUTH`

### Обновленные зависимости

Роутеры обновлены для использования условных зависимостей:

```python
# Было
user: dict[str, Any] = Depends(require_write)

# Стало
user: dict[str, Any] = Depends(require_write_conditional)
```

## Тестирование

Используйте скрипт `test_auth_conditional.py` для проверки функциональности:

```bash
# Запустите сервер
python main.py

# В другом терминале
python test_auth_conditional.py
```

Скрипт проверит:
- Эндпоинты без авторизации работают всегда
- Эндпоинты с обязательной авторизацией всегда требуют API ключ
- Условные эндпоинты ведут себя согласно настройке `REQUIRE_AUTH`

## Безопасность

⚠️ **Важно**: Никогда не устанавливайте `REQUIRE_AUTH=false` в production окружении!

Эта настройка предназначена только для:
- Локальной разработки
- Тестирования
- Демонстрационных целей

В production всегда используйте `REQUIRE_AUTH=true` и настройте валидные API ключи.
