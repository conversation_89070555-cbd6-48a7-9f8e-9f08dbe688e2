# Converter Rate Limits - High Throughput Configuration

## Обзор

Эндпойнты конвертеров (например, `/api/convert/ttml-to-txt`) теперь имеют специальные лимиты с повышенной пропускной способностью, поскольку операции конвертации являются легковесными и быстрыми.

## Новые лимиты для конвертеров

### Конфигурация по умолчанию

| Период | Лимит | Описание |
|--------|-------|----------|
| **Минута** | 100 запросов | Высокая пропускная способность для быстрых операций |
| **Час** | 6,000 запросов | 100 запросов/мин × 60 мин |
| **День** | 144,000 запросов | 100 запросов/мин × 60 мин × 24 часа |
| **Burst** | 50 запросов | Увеличенный burst лимит для пакетной обработки |

### Сравнение с другими эндпойнтами

| Эндпойнт | Запросов/минуту | Причина |
|----------|-----------------|---------|
| **Конвертеры** | 100 | Легковесные операции, быстрая обработка |
| Subtitles | 30 | Ресурсоемкие операции с YouTube API |
| Summarize | 5 | Очень ресурсоемкие операции с AI |
| Tasks | 30 | Операции с базой данных |
| Auth | 20 | Безопасность |
| Health | 120 | Мониторинг |

## Переменные окружения

Добавлены новые переменные для настройки лимитов конвертеров:

```bash
# Converter endpoints (lightweight operations - high throughput)
CONVERT_RATE_LIMIT_MINUTE=100
CONVERT_RATE_LIMIT_HOUR=6000
CONVERT_RATE_LIMIT_DAY=144000
CONVERT_BURST_LIMIT=50
```

## Технические детали

### Обновленные компоненты

1. **`core/config.py`** - добавлены новые переменные конфигурации
2. **`api/middleware/rate_limiting.py`** - добавлено поле `convert` в `EndpointLimits` и логика для `REQUIRE_AUTH`
3. **`core/app.py`** - применение новых лимитов в middleware
4. **`.env.example`** - документация переменных окружения

### Логика авторизации и лимитов

**Важно**: Поведение rate limiting зависит от настройки `REQUIRE_AUTH`:

- **`REQUIRE_AUTH=false`** (рекомендуется для конвертеров): Используются только endpoint limits, игнорируются user limits
- **`REQUIRE_AUTH=true`**: Используются более строгие лимиты между endpoint и user limits

### Логика определения эндпойнта

Middleware автоматически определяет эндпойнты конвертеров по префиксу `/api/convert/`:

```python
def _get_endpoint_config(self, path: str) -> RateLimitConfig:
    if path.startswith("/api/convert"):
        return self.endpoint_limits.convert
    # ... другие эндпойнты

def _get_rate_limit_config(self, path: str, permissions: list) -> RateLimitConfig:
    endpoint_config = self._get_endpoint_config(path)

    # Если авторизация отключена, используем только endpoint limits
    if not settings.REQUIRE_AUTH:
        return endpoint_config

    # Иначе используем более строгие лимиты
    user_config = self._get_user_config(permissions)
    return RateLimitConfig(
        requests_per_minute=min(endpoint_config.requests_per_minute, user_config.requests_per_minute),
        # ... остальные поля
    )
```

### Burst лимиты

Конвертеры имеют увеличенный burst лимит (50 запросов за 10 секунд) для обработки пакетной обработки файлов.

## Улучшения клиента

### Параллельная обработка

Клиент `client/utils/convert_ttml_files.py` теперь поддерживает параллельную обработку файлов:

```bash
# Обработка с 10 одновременными запросами (по умолчанию)
python client/utils/convert_ttml_files.py /path/to/ttml/files

# Обработка с 20 одновременными запросами
python client/utils/convert_ttml_files.py /path/to/ttml/files --concurrent 20

# Обработка с отладкой
python client/utils/convert_ttml_files.py /path/to/ttml/files --concurrent 15 --debug
```

### Автоматические повторные попытки

Клиент автоматически обрабатывает ошибки rate limiting (429) с повторными попытками:

- **Максимум 3 повторные попытки** при получении 429 ошибки
- **Автоматическая пауза** согласно заголовку `Retry-After`
- **Экспоненциальная задержка** для других ошибок

### Производительность

**До улучшений**: 4-19 файлов за раз (последовательная обработка)
**После улучшений**: 50+ файлов одновременно (параллельная обработка)

**Рекомендуемые настройки**:
- **10-15 одновременных запросов** для стабильной работы
- **20+ запросов** для максимальной скорости (при хорошем соединении)
- **5 запросов** для медленных соединений

## Мониторинг

### HTTP заголовки

Все запросы к конвертерам возвращают заголовки с информацией о лимитах:

```
X-RateLimit-Limit-Minute: 100
X-RateLimit-Limit-Hour: 6000
X-RateLimit-Limit-Day: 144000
X-RateLimit-Remaining-Minute: 95
X-RateLimit-Remaining-Hour: 5995
X-RateLimit-Remaining-Day: 143995
X-RateLimit-Reset: 1640995200
```

### Ответ при превышении лимита (429)

```json
{
    "error": "rate_limit_exceeded",
    "message": "Too many requests. Please slow down.",
    "details": {
        "retry_after": 60,
        "limit_per_minute": 100,
        "limit_per_hour": 6000,
        "limit_per_day": 144000,
        "requests_per_minute": 100,
        "requests_per_hour": 1500,
        "requests_per_day": 25000
    }
}
```

## Тестирование

Создан тестовый скрипт `test_converter_rate_limits.py` для проверки работы лимитов:

```bash
# Запуск тестов rate limiting для конвертеров
python test_converter_rate_limits.py
```

Тест проверяет:
- Обычные лимиты (30 запросов с 5 одновременными)
- Burst лимиты (25 быстрых запросов)
- Время отклика и пропускную способность

## Расширяемость

Система готова для добавления новых конвертеров:

- Все эндпойнты с префиксом `/api/convert/` автоматически получают высокие лимиты
- Можно добавлять новые конвертеры без изменения конфигурации rate limiting
- Лимиты легко настраиваются через переменные окружения

## Рекомендации по настройке

### Для максимальной производительности конвертеров

**Рекомендуется**: Установить `REQUIRE_AUTH=false` в `.env` файле:

```bash
# Отключить авторизацию для максимальной производительности конвертеров
REQUIRE_AUTH=false
```

**Преимущества**:
- Конвертеры получают полные 100 запросов/минуту
- Нет ограничений анонимных пользователей (10/мин)
- Упрощенная архитектура без управления токенами

### Если требуется авторизация

Если необходимо оставить `REQUIRE_AUTH=true`, увеличьте лимиты для анонимных пользователей:

```bash
REQUIRE_AUTH=true
ANONYMOUS_RATE_LIMIT_MINUTE=120  # Увеличить с 10 до 120
ANONYMOUS_RATE_LIMIT_HOUR=7200
ANONYMOUS_RATE_LIMIT_DAY=172800
```

## Рекомендации по использованию

1. **Для клиентов**: Используйте заголовки `X-RateLimit-*` для мониторинга использования
2. **Для разработчиков**: Новые конвертеры должны размещаться по пути `/api/convert/*`
3. **Для администраторов**: Мониторьте метрики и при необходимости корректируйте лимиты
4. **Для производительности**: Рассмотрите отключение авторизации (`REQUIRE_AUTH=false`) для конвертеров

## Примеры использования

### Массовая конвертация файлов

```python
import asyncio
import aiohttp

async def convert_multiple_files(ttml_files):
    async with aiohttp.ClientSession() as session:
        tasks = []
        for ttml_content in ttml_files:
            task = convert_single_file(session, ttml_content)
            tasks.append(task)
        
        # Обрабатываем по 10 файлов одновременно
        for i in range(0, len(tasks), 10):
            batch = tasks[i:i+10]
            results = await asyncio.gather(*batch)
            # Обрабатываем результаты
            await asyncio.sleep(0.1)  # Небольшая пауза между батчами
```

### Обработка ошибок rate limiting

```python
async def convert_with_retry(session, ttml_content, max_retries=3):
    for attempt in range(max_retries):
        async with session.post("/api/convert/ttml-to-txt", json={"ttml_text": ttml_content}) as response:
            if response.status == 200:
                return await response.json()
            elif response.status == 429:
                retry_after = int(response.headers.get("Retry-After", 60))
                await asyncio.sleep(retry_after)
            else:
                raise Exception(f"Conversion failed: {response.status}")
    
    raise Exception("Max retries exceeded")
```
