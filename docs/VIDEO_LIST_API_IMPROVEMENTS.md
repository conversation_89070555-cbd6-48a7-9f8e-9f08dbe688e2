# Video List API Improvements

## Проблема
Эндпойнт `/api/subtitles/get_id` не возвращал результаты извлечения video IDs из плейлистов и каналов YouTube, хотя обработка проходила успешно.

## Исправления

### 1. Исправлена валидация URL
- **Проблема**: Валидация `validate_youtube_url` была слишком строгой и отклоняла URL каналов и плейлистов
- **Решение**: Создана новая функция `validate_youtube_video_list_url` в `models/validators.py`
- **Поддерживаемые форматы**:
  - Отдельные видео: `youtube.com/watch?v=ID`, `youtu.be/ID`
  - Плейлисты: `youtube.com/playlist?list=ID`
  - Каналы: `youtube.com/@username/videos`, `youtube.com/channel/ID/videos`

### 2. Исправлен API эндпойнт для получения результатов
- **Проблема**: `/api/task/{task_id}` не поддерживал тип `VideoListResponse`
- **Решение**: Обновлен `api/routers/tasks.py` для поддержки всех типов задач
- **Изменения**: `response_model=SubtitleResponse | SummarizeResponse | VideoListResponse`

### 3. Добавлена синхронная обработка
- **Мотивация**: Извлечение video IDs занимает 1-5 секунд - подходит для синхронного ответа
- **Реализация**: Обновлен `/api/subtitles/get_id` с параметром `sync=true` (по умолчанию)
- **Преимущества**: 
  - Мгновенный ответ для большинства случаев
  - Автоматический fallback к асинхронной обработке при таймауте
  - Лучший UX для клиентов

### 4. Добавлен отдельный асинхронный эндпойнт
- **Эндпойнт**: `/api/subtitles/get_id_async`
- **Назначение**: Для больших плейлистов или когда предпочтителен асинхронный workflow
- **Поведение**: Всегда использует очередь задач

## Архитектурные решения

### Синхронная vs Асинхронная обработка

**Синхронная обработка (`/api/subtitles/get_id`)**:
- ✅ Быстрый ответ (1-5 секунд)
- ✅ Простой для клиентов (один запрос)
- ✅ Автоматический fallback к async при проблемах
- ❌ Блокирует worker thread на время обработки
- ❌ Может вызвать timeout для очень больших плейлистов

**Асинхронная обработка (`/api/subtitles/get_id_async`)**:
- ✅ Не блокирует сервер
- ✅ Подходит для больших плейлистов
- ✅ Лучше для высоконагруженных систем
- ❌ Требует polling или WebSocket для получения результатов
- ❌ Более сложный workflow для клиентов

### Рекомендации по использованию

1. **Для небольших каналов/плейлистов (< 100 видео)**: Используйте `/api/subtitles/get_id` (синхронный)
2. **Для больших каналов/плейлистов (> 100 видео)**: Используйте `/api/subtitles/get_id_async`
3. **Для интеграций**: Начните с синхронного, переключитесь на асинхронный при необходимости

## Примеры использования

### Синхронный запрос
```bash
curl -X POST "http://localhost:8000/api/subtitles/get_id" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"url": "https://www.youtube.com/@ByteMonk/videos"}'
```

**Ответ (200 OK)**:
```json
{
    "status": "completed",
    "task_id": "sync_1749172005123",
    "client_uid": null,
    "video_ids": ["dQw4w9WgXcQ", "oHg5SJYRHA0", "..."],
    "error": null
}
```

### Асинхронный запрос
```bash
curl -X POST "http://localhost:8000/api/subtitles/get_id_async" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"url": "https://www.youtube.com/@ByteMonk/videos"}'
```

**Ответ (202 Accepted)**:
```json
{
    "status": "pending",
    "task_id": "list_171c3333_1749172005",
    "client_uid": null,
    "video_ids": null,
    "error": null
}
```

**Получение результата**:
```bash
curl -X GET "http://localhost:8000/api/task/list_171c3333_1749172005" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Дальнейшие улучшения

### 1. Кеширование результатов
- Кешировать video IDs для каналов/плейлистов на определенное время
- Использовать Redis или in-memory cache
- TTL: 1-6 часов в зависимости от типа контента

### 2. Пагинация для больших результатов
- Добавить параметры `limit` и `offset`
- Полезно для каналов с тысячами видео

### 3. Фильтрация по дате
- Параметры `from_date` и `to_date`
- Полезно для получения только новых видео

### 4. Метрики и мониторинг
- Отслеживание времени обработки
- Статистика по размерам плейлистов
- Алерты при превышении таймаутов

### 5. Rate limiting
- Ограничения на количество запросов к YouTube API
- Интеллектуальная очередь с приоритетами

## Тестирование

Создан тест-скрипт `test_video_list_api.py` для проверки функциональности:
```bash
python test_video_list_api.py
```

Также создан `test_video_list_validation.py` для проверки валидации URL.
