# API Документация

Эта документация описывает все доступные эндпойнты API сервера для работы с субтитрами YouTube и суммаризацией текста.

## Общая информация

- **Базовый URL**: `http://localhost:8000`
- **WebSocket URL**: `ws://localhost:8000`
- **Формат данных**: JSON
- **Кодировка**: UTF-8

## Эндпойнты

### Проверка доступности сервера

```http
POST /ping
```

#### Ответ

```json
{
    "message": "pong"
}
```

- **Код состояния**: 200 OK

### Получение списка video ID из плейлиста/канала

```http
POST /subtitles/get_id
```

Быстрое извлечение video ID из YouTube плейлистов, каналов или отдельных видео.

**Режимы работы:**
- **Синхронный (по умолчанию)**: Обрабатывает запросы синхронно для быстрого ответа (1-5 секунд)
- **Асинхронный**: Можно принудительно включить через переменную окружения `ENABLE_SYNC_VIDEO_LIST=false`

**Важно**: Синхронные задачи (task_id начинается с `sync_`) завершаются немедленно и не сохраняются в системе. Попытка получить статус такой задачи через `/api/task/{id}` вернет ошибку 404.

#### Тело запроса

```json
{
    "url": "https://www.youtube.com/@ByteMonk/videos"
}
```

#### Параметры запроса

- `sync` (boolean, optional): `true` для синхронной обработки (по умолчанию), `false` для асинхронной

#### Поддерживаемые форматы URL

- Отдельные видео: `https://www.youtube.com/watch?v=VIDEO_ID`
- Плейлисты: `https://www.youtube.com/playlist?list=PLAYLIST_ID`
- Каналы: `https://www.youtube.com/@username/videos`
- Каналы: `https://www.youtube.com/channel/CHANNEL_ID/videos`

#### Ответ (синхронный)

```json
{
    "status": "completed",
    "task_id": "sync_1749172005123",
    "video_ids": ["dQw4w9WgXcQ", "oHg5SJYRHA0", "..."],
    "error": null
}
```

- **Код состояния**: 200 OK (синхронно) или 202 Accepted (асинхронно)

### Получение списка video ID (асинхронно)

```http
POST /subtitles/get_id_async
```

Асинхронное извлечение video ID через очередь задач. Используйте для больших плейлистов или когда предпочитаете асинхронный workflow.

#### Тело запроса

```json
{
    "url": "https://www.youtube.com/@ByteMonk/videos"
}
```

#### Ответ

```json
{
    "status": "pending",
    "task_id": "list_171c3333_1749172005",
    "video_ids": null,
    "error": null
}
```

- **Код состояния**: 202 Accepted

### Создание задачи на получение субтитров

```http
POST /subtitles
```

#### Тело запроса

```json
{
    "url": "https://www.youtube.com/watch?v=VIDEO_ID"
}
```

#### Ответ

```json
{
    "task_id": "string",
    "status": "pending",
    "title": "string",
    "original_language": "string",
    "en_subtitles": [],
    "ru_subtitles": [],
    "error": null
}
```

- **Коды состояния**:
  - 200: Задача успешно создана
  - 400: Неверный URL или формат запроса

### Создание задачи на суммаризацию текста

```http
POST /summarize
```

#### Тело запроса

```json
{
    "og_text": "string",
    "mode": "default"
}
```

- **mode** (опционально): Режим суммаризации ("default" если не указан)

#### Ответ

```json
{
    "task_id": "string",
    "status": "pending",
    "summary": null,
    "error": null
}
```

- **Коды состояния**:
  - 200: Задача успешно создана
  - 400: Неверный формат запроса

### Суммаризация файла

```http
POST /summarize/file
```

#### Параметры запроса

- **file**: Файл для суммаризации (multipart/form-data)
- **mode** (опционально): Режим суммаризации

#### Поддерживаемые форматы файлов

- `.txt`: Текстовые файлы
- `.md`: Markdown файлы

#### Ответ

```json
{
    "task_id": "string",
    "status": "pending",
    "summary": null,
    "error": null
}
```

- **Коды состояния**:
  - 200: Задача успешно создана
  - 400: Неверный формат файла или пустой файл
  - 500: Ошибка обработки файла

### Получение статуса задачи

```http
GET /task/{task_id}
```

#### Ответ для задачи с субтитрами

```json
{
    "task_id": "string",
    "status": "completed",
    "title": "string",
    "original_language": "string",
    "en_subtitles": [
        {
            "start": "float",
            "end": "float",
            "text": "string"
        }
    ],
    "ru_subtitles": [
        {
            "start": "float",
            "end": "float",
            "text": "string"
        }
    ],
    "error": null
}
```

#### Ответ для задачи с суммаризацией

```json
{
    "task_id": "string",
    "status": "completed",
    "summary": "string",
    "error": null
}
```

#### Ответ для задачи со списком video ID

```json
{
    "task_id": "string",
    "status": "completed",
    "client_uid": null,
    "video_ids": ["dQw4w9WgXcQ", "oHg5SJYRHA0", "..."],
    "error": null
}
```

- **Коды состояния**:
  - 200: Задача найдена
  - 404: Задача не найдена

## WebSocket API

### Получение субтитров в реальном времени

```
ws://localhost:8000/ws/subtitles
```

#### Формат сообщения для начала обработки

```json
{
    "url": "https://www.youtube.com/watch?v=VIDEO_ID"
}
```

#### Формат ответных сообщений

```json
{
    "type": "status_update",
    "task_id": "string",
    "status": "string",
    "title": "string",
    "original_language": "string",
    "en_subtitles": [],
    "ru_subtitles": [],
    "error": null
}
```

### Суммаризация в реальном времени

```
ws://localhost:8000/ws/summarize
```

#### Формат сообщения для текста

```json
{
    "type": "text",
    "content": "string",
    "mode": "default"
}
```

#### Формат сообщения для файла

```json
{
    "type": "file",
    "content": "base64_encoded_string",
    "filename": "example.txt",
    "mode": "default"
}
```

#### Формат ответных сообщений

```json
{
    "type": "status",
    "task_id": "string",
    "status": "string",
    "summary": "string",
    "error": null
}
```

## Общие характеристики

### Статусы задач

- `pending`: Задача создана и ожидает обработки
- `processing`: Задача обрабатывается
- `completed`: Задача успешно завершена
- `failed`: Произошла ошибка при обработке задачи

### Тайм-ауты и ограничения

- **WebSocket тайм-аут**: 90 секунд бездействия
- **Heartbeat интервал**: 30 секунд
- **Формат файлов**: Только UTF-8 кодировка
- **Поддерживаемые форматы**: .txt, .md

### Обработка ошибок

Все ошибки возвращаются в формате:

```json
{
    "error": "Описание ошибки"
}
```

### Механизм переподключения

При потере соединения WebSocket клиент должен:

1. Попытаться переподключиться
2. При успешном переподключении отправить исходный запрос
3. Использовать полученный task_id для отслеживания прогресса

### Поддержание соединения

1. Сервер отправляет heartbeat каждые 30 секунд
2. Клиент должен отвечать на ping сообщения
3. При отсутствии активности более 90 секунд соединение закрывается