"""Application configuration settings."""

from dotenv import load_dotenv
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

# Load environment variables from .env file if it exists
load_dotenv()


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""

    # Application settings
    APP_NAME: str = "YouTube Subtitles and Text Summarization API"
    APP_VERSION: str = "0.1.0"
    DEBUG: bool = False

    # Server settings
    HOST: str = Field(default="0.0.0.0", description="Server host")
    PORT: int = Field(default=8000, description="Server port")
    RELOAD: bool = Field(
        default=False, description="Enable auto-reload for development"
    )

    # API settings
    API_PREFIX: str = "/api"
    DOCS_URL: str = "/docs"
    REDOC_URL: str = "/redoc"
    OPENAPI_URL: str = "/openapi.json"

    # Task queue settings
    MAX_SUBTITLE_WORKERS: int = 1
    MAX_SUMMARIZE_WORKERS: int = 2
    SUBTITLE_QUEUE_SIZE: int = 10
    SUMMARIZE_QUEUE_SIZE: int = 10

    # Video list extraction settings
    ENABLE_SYNC_VIDEO_LIST: bool = Field(
        default=True,
        description="Enable synchronous video list extraction for fast response (1-5 seconds). Set to False to force async processing.",
    )

    # WebSocket settings
    WEBSOCKET_PING_INTERVAL: int = 30  # seconds
    WEBSOCKET_PING_TIMEOUT: int = 5  # seconds

    # Logging settings
    LOG_LEVEL: str = Field(
        default="INFO",
        description="Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)",
    )
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # Structured logging settings
    STRUCTURED_LOGGING: bool = True  # Use structlog for structured logging
    LOG_JSON_FORMAT: bool = False  # Use JSON format (True for production)
    LOG_REQUEST_BODY: bool = (
        False  # Log request bodies (be careful with sensitive data)
    )
    LOG_RESPONSE_BODY: bool = (
        False  # Log response bodies (be careful with large responses)
    )
    LOG_EXCLUDE_PATHS: list = [
        "/health",
        "/metrics",
        "/docs",
        "/redoc",
        "/openapi.json",
    ]

    # YouTube API settings (if any)
    YOUTUBE_API_KEY: str | None = None

    # Proxy settings
    SOCKS5_PROXY: str | None = None

    # Cookies file for YouTube
    COOKIES_FILE: str | None = None

    # Gemini AI API key
    GEM_API: str | None = None

    # Database settings
    DATABASE_TYPE: str = "sqlite"
    POSTGRES_HOST: str | None = None
    POSTGRES_PORT: int | None = None
    POSTGRES_USER: str | None = None
    POSTGRES_PASSWORD: str | None = None
    POSTGRES_DB: str | None = None

    # CORS settings
    CORS_ORIGINS: str = Field(
        default="*",
        description="Comma-separated list of allowed origins. Use '*' for development only! Example: 'https://yourdomain.com,https://app.yourdomain.com'",
    )
    CORS_ALLOW_CREDENTIALS: bool = Field(
        default=True, description="Allow credentials in CORS requests"
    )
    CORS_ALLOW_METHODS: str = Field(
        default="GET,POST,PUT,DELETE,OPTIONS",
        description="Comma-separated list of allowed HTTP methods",
    )
    CORS_ALLOW_HEADERS: str = Field(
        default="Authorization,Content-Type,X-API-Key",
        description="Comma-separated list of allowed headers",
    )

    # Security settings
    SECURITY_HEADERS_ENABLED: bool = Field(
        default=True, description="Enable security headers middleware"
    )

    # Monitoring settings
    PROMETHEUS_ENABLED: bool = Field(
        default=True, description="Enable Prometheus metrics collection"
    )

    # Authentication settings
    REQUIRE_AUTH: bool = True  # Set to False to disable authentication
    API_KEYS: str | None = None  # JSON string of API keys for production

    # Rate limiting settings
    RATE_LIMITING_ENABLED: bool = True  # Enable/disable rate limiting

    # Anonymous user limits (per minute/hour/day)
    # Note: These limits only apply when REQUIRE_AUTH=true
    # When REQUIRE_AUTH=false, only endpoint limits are used
    ANONYMOUS_RATE_LIMIT_MINUTE: int = 10
    ANONYMOUS_RATE_LIMIT_HOUR: int = 100
    ANONYMOUS_RATE_LIMIT_DAY: int = 500

    # Read-only user limits
    READ_RATE_LIMIT_MINUTE: int = 30
    READ_RATE_LIMIT_HOUR: int = 500
    READ_RATE_LIMIT_DAY: int = 2000

    # Read-write user limits
    WRITE_RATE_LIMIT_MINUTE: int = 60
    WRITE_RATE_LIMIT_HOUR: int = 1000
    WRITE_RATE_LIMIT_DAY: int = 5000

    # Admin user limits
    ADMIN_RATE_LIMIT_MINUTE: int = 120
    ADMIN_RATE_LIMIT_HOUR: int = 2000
    ADMIN_RATE_LIMIT_DAY: int = 10000

    # Endpoint-specific limits (subtitles are more resource intensive)
    SUBTITLES_RATE_LIMIT_MINUTE: int = 10
    SUBTITLES_RATE_LIMIT_HOUR: int = 100
    SUBTITLES_RATE_LIMIT_DAY: int = 500

    # Summarization limits (very resource intensive)
    SUMMARIZE_RATE_LIMIT_MINUTE: int = 5
    SUMMARIZE_RATE_LIMIT_HOUR: int = 50
    SUMMARIZE_RATE_LIMIT_DAY: int = 200

    # Converter limits (lightweight operations - high throughput)
    CONVERT_RATE_LIMIT_MINUTE: int = 100  # 100 requests per minute
    CONVERT_RATE_LIMIT_HOUR: int = 6000  # 100 * 60 = 6000 per hour
    CONVERT_RATE_LIMIT_DAY: int = 144000  # 100 * 60 * 24 = 144000 per day
    CONVERT_BURST_LIMIT: int = 50  # High burst limit for batch processing
    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", case_sensitive=True, extra="ignore"
    )

    @field_validator("LOG_LEVEL")
    @classmethod
    def validate_log_level(cls, v: str) -> str:
        """Validate that LOG_LEVEL is a valid logging level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"LOG_LEVEL must be one of {valid_levels}")
        return v.upper()

    @field_validator("CORS_ORIGINS")
    @classmethod
    def validate_cors_origins(cls, v: str) -> str:
        """Validate CORS origins (warning is handled in configure_cors function)."""
        # Note: Security warning is handled in core/app.py configure_cors()
        # to use consistent loguru formatting instead of Python warnings
        return v


# Create settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the application settings."""
    return settings
