"""
Integration tests for WebSocket functionality.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient

from models.schemas import MessageType


class TestWebSocketConnection:
    """Test WebSocket connection functionality."""

    def test_websocket_subtitles_connection(self, client: TestClient):
        """Test WebSocket connection to subtitles endpoint."""
        with client.websocket_connect("/ws/subtitles") as websocket:
            # Connection should be established
            assert websocket is not None

    def test_websocket_summarize_connection(self, client: TestClient):
        """Test WebSocket connection to summarize endpoint."""
        with client.websocket_connect("/ws/summarize") as websocket:
            # Connection should be established
            assert websocket is not None

    def test_websocket_invalid_endpoint(self, client: TestClient):
        """Test WebSocket connection to invalid endpoint."""
        with pytest.raises(Exception):
            # Should fail to connect to non-existent endpoint
            with client.websocket_connect("/ws/invalid"):
                pass


class TestWebSocketSubtitles:
    """Test WebSocket subtitle functionality."""

    def test_websocket_subtitles_valid_request(
        self, client: TestClient, sample_youtube_urls
    ):
        """Test WebSocket subtitle request with valid URL."""
        with client.websocket_connect("/ws/subtitles") as websocket:
            # Send subtitle request
            request_data = {"url": sample_youtube_urls["valid"][0]}
            websocket.send_json(request_data)

            # Should receive a response
            try:
                response = websocket.receive_json()
                assert "type" in response
                assert "task_id" in response
                assert "status" in response
            except Exception:
                # WebSocket might close immediately in test environment
                pass

    def test_websocket_subtitles_invalid_url(
        self, client: TestClient, sample_youtube_urls
    ):
        """Test WebSocket subtitle request with invalid URL."""
        with client.websocket_connect("/ws/subtitles") as websocket:
            # Send invalid subtitle request
            request_data = {"url": sample_youtube_urls["invalid"][0]}
            websocket.send_json(request_data)

            # Should receive error response
            try:
                response = websocket.receive_json()
                # Should contain error information
                assert "error" in response or "status" in response
            except Exception:
                # WebSocket might close on error
                pass

    def test_websocket_subtitles_malformed_request(self, client: TestClient):
        """Test WebSocket subtitle request with malformed data."""
        with client.websocket_connect("/ws/subtitles") as websocket:
            # Send malformed request
            request_data = {"invalid_field": "invalid_value"}
            websocket.send_json(request_data)

            # Should handle gracefully
            try:
                response = websocket.receive_json()
                # Should contain error information
                assert "error" in response or "status" in response
            except Exception:
                # WebSocket might close on error
                pass

    def test_websocket_subtitles_with_client_uid(
        self, client: TestClient, sample_youtube_urls
    ):
        """Test WebSocket subtitle request with deprecated client_uid (should be ignored)."""
        with client.websocket_connect("/ws/subtitles") as websocket:
            # Send request with deprecated client_uid
            request_data = {
                "url": sample_youtube_urls["valid"][0],
                "client_uid": "deprecated_client_123",  # This should be ignored
            }
            websocket.send_json(request_data)

            # Should still work (client_uid is ignored in processing)
            try:
                response = websocket.receive_json()
                assert "task_id" in response
                # Verify that the response doesn't include client_uid (field has been removed)
                assert "client_uid" not in response
            except Exception:
                pass


class TestWebSocketSummarize:
    """Test WebSocket summarization functionality."""

    def test_websocket_summarize_text_request(
        self, client: TestClient, sample_text_content
    ):
        """Test WebSocket text summarization request."""
        with client.websocket_connect("/ws/summarize") as websocket:
            # Send text summarization request
            request_data = {
                "type": MessageType.TEXT_INPUT,
                "content": sample_text_content["medium"],
                "mode": "default",
            }
            websocket.send_json(request_data)

            # Should receive a response
            try:
                response = websocket.receive_json()
                assert "type" in response
                assert "task_id" in response
                assert "status" in response
            except Exception:
                pass

    def test_websocket_summarize_file_request(
        self, client: TestClient, sample_file_uploads
    ):
        """Test WebSocket file summarization request."""
        with client.websocket_connect("/ws/summarize") as websocket:
            # Send file summarization request
            file_data = sample_file_uploads["text_file"]
            request_data = {
                "type": MessageType.FILE_INPUT,
                "content": file_data["content"],
                "filename": file_data["filename"],
                "mode": "default",
            }
            websocket.send_json(request_data)

            # Should receive a response
            try:
                response = websocket.receive_json()
                assert "type" in response
                assert "task_id" in response
            except Exception:
                pass

    def test_websocket_summarize_text_too_short(
        self, client: TestClient, sample_text_content
    ):
        """Test WebSocket summarization with text too short."""
        with client.websocket_connect("/ws/summarize") as websocket:
            # Send request with text too short
            request_data = {
                "type": MessageType.TEXT_INPUT,
                "content": sample_text_content["short"],
                "mode": "default",
            }
            websocket.send_json(request_data)

            # Should receive error response
            try:
                response = websocket.receive_json()
                assert "error" in response or "status" in response
            except Exception:
                pass

    def test_websocket_summarize_invalid_type(
        self, client: TestClient, sample_text_content
    ):
        """Test WebSocket summarization with invalid message type."""
        with client.websocket_connect("/ws/summarize") as websocket:
            # Send request with invalid type
            request_data = {
                "type": "invalid_type",
                "content": sample_text_content["medium"],
            }
            websocket.send_json(request_data)

            # Should receive error response
            try:
                response = websocket.receive_json()
                assert "error" in response
            except Exception:
                pass


class TestWebSocketAuthentication:
    """Test WebSocket authentication functionality."""

    def test_websocket_without_auth_when_required(self, client: TestClient):
        """Test WebSocket connection without auth when required."""
        # This test depends on auth configuration
        try:
            with client.websocket_connect("/ws/subtitles") as websocket:
                # Should either work (if auth disabled) or fail (if auth required)
                assert websocket is not None
        except Exception:
            # Expected if auth is required and not provided
            pass

    def test_websocket_with_invalid_auth(self, client: TestClient):
        """Test WebSocket connection with invalid authentication."""
        # WebSocket auth might be handled differently than HTTP auth
        try:
            with client.websocket_connect(
                "/ws/subtitles", headers={"Authorization": "Bearer invalid_key"}
            ) as websocket:
                assert websocket is not None
        except Exception:
            # Expected if auth validation fails
            pass

    def test_websocket_with_valid_auth(self, client: TestClient, auth_headers):
        """Test WebSocket connection with valid authentication."""
        try:
            with client.websocket_connect(
                "/ws/subtitles", headers=auth_headers
            ) as websocket:
                assert websocket is not None
        except Exception:
            # Might fail if WebSocket auth is not properly configured in tests
            pass


class TestWebSocketErrorHandling:
    """Test WebSocket error handling."""

    def test_websocket_invalid_json(self, client: TestClient):
        """Test WebSocket with invalid JSON data."""
        with client.websocket_connect("/ws/subtitles") as websocket:
            # Send invalid JSON
            try:
                websocket.send_text("invalid json data")
                response = websocket.receive_json()
                assert "error" in response
            except Exception:
                # WebSocket might close on invalid data
                pass

    def test_websocket_empty_message(self, client: TestClient):
        """Test WebSocket with empty message."""
        with client.websocket_connect("/ws/subtitles") as websocket:
            try:
                websocket.send_json({})
                response = websocket.receive_json()
                assert "error" in response or "status" in response
            except Exception:
                pass

    def test_websocket_connection_close(self, client: TestClient):
        """Test WebSocket connection close handling."""
        with client.websocket_connect("/ws/subtitles") as websocket:
            # Close connection
            websocket.close()

            # Should handle close gracefully
            try:
                websocket.receive_json()
            except Exception:
                # Expected when connection is closed
                pass


class TestWebSocketMessageFlow:
    """Test WebSocket message flow and status updates."""

    def test_websocket_status_updates(self, client: TestClient, sample_youtube_urls):
        """Test WebSocket status update flow."""
        with client.websocket_connect("/ws/subtitles") as websocket:
            # Send request
            request_data = {"url": sample_youtube_urls["valid"][0]}
            websocket.send_json(request_data)

            # Should receive status updates
            try:
                # First response - task created
                response1 = websocket.receive_json()
                assert "task_id" in response1
                assert "status" in response1

                # Might receive additional status updates
                # In real implementation, would receive PROCESSING, then COMPLETED

            except Exception:
                # In test environment, might not get full flow
                pass

    def test_websocket_multiple_requests(self, client: TestClient, sample_youtube_urls):
        """Test multiple requests on same WebSocket connection."""
        with client.websocket_connect("/ws/subtitles") as websocket:
            # Send multiple requests
            for i, url in enumerate(sample_youtube_urls["valid"][:2]):
                request_data = {"url": url}
                websocket.send_json(request_data)

                try:
                    response = websocket.receive_json()
                    assert "task_id" in response
                    # Each request should get unique task_id
                except Exception:
                    pass

    def test_websocket_concurrent_connections(
        self, client: TestClient, sample_youtube_urls
    ):
        """Test multiple concurrent WebSocket connections."""
        # Test that multiple clients can connect simultaneously
        connections = []

        try:
            for i in range(3):
                ws = client.websocket_connect("/ws/subtitles")
                connections.append(ws)

            # All connections should be established
            assert len(connections) == 3

            # Send requests on each connection
            for i, ws in enumerate(connections):
                with ws as websocket:
                    request_data = {"url": sample_youtube_urls["valid"][0]}
                    websocket.send_json(request_data)

                    try:
                        response = websocket.receive_json()
                        assert "task_id" in response
                    except Exception:
                        pass

        except Exception:
            # Might fail in test environment
            pass
        finally:
            # Clean up connections
            for ws in connections:
                try:
                    ws.close()
                except Exception:
                    pass


class TestWebSocketPerformance:
    """Test WebSocket performance characteristics."""

    def test_websocket_rapid_requests(self, client: TestClient, sample_youtube_urls):
        """Test rapid WebSocket requests."""
        with client.websocket_connect("/ws/subtitles") as websocket:
            # Send rapid requests
            for i in range(5):
                request_data = {"url": sample_youtube_urls["valid"][0]}
                websocket.send_json(request_data)

                try:
                    response = websocket.receive_json()
                    # Should handle rapid requests gracefully
                    assert "task_id" in response or "error" in response
                except Exception:
                    # Might be rate limited or connection closed
                    pass

    def test_websocket_large_message(self, client: TestClient, sample_text_content):
        """Test WebSocket with large message."""
        with client.websocket_connect("/ws/summarize") as websocket:
            # Send large text content
            request_data = {
                "type": MessageType.TEXT_INPUT,
                "content": sample_text_content["medium"] * 10,  # Make it larger
                "mode": "default",
            }

            try:
                websocket.send_json(request_data)
                response = websocket.receive_json()
                # Should handle large messages
                assert "task_id" in response or "error" in response
            except Exception:
                # Might fail due to size limits
                pass
