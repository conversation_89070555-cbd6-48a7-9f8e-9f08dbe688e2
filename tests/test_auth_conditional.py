#!/usr/bin/env python3
"""
Test script to verify conditional authentication functionality.
This script tests that REQUIRE_AUTH=true/false works correctly for all endpoints.
"""

import sys
from typing import Any

import requests

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_API_KEY = "demo_key_12345"  # Default demo key

# Test endpoints that should respect REQUIRE_AUTH
CONDITIONAL_AUTH_ENDPOINTS = [
    (
        "POST",
        "/api/subtitles/get_id",
        {"url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ"},
    ),
    ("POST", "/api/subtitles", {"url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ"}),
    ("POST", "/api/summarize", {"og_text": "This is a test text for summarization."}),
    ("GET", "/api/task/test-task-id", {}),
    ("GET", "/api/alerts", {}),
    ("GET", "/api/logs", {}),
    (
        "POST",
        "/api/convert/ttml-to-txt",
        {"ttml_content": "<tt><body><div><p>Test subtitle</p></div></body></tt>"},
    ),
]

# Endpoints that should never require auth
NO_AUTH_ENDPOINTS = [
    ("POST", "/ping", {}),
    ("GET", "/health", {}),
    ("GET", "/metrics", {}),
]

# Endpoints that should always require auth
ALWAYS_AUTH_ENDPOINTS = [
    ("GET", "/api/auth/keys", {}),
    ("GET", "/api/auth/me", {}),
]


def make_request(
    method: str, endpoint: str, data: dict[str, Any], use_auth: bool = False
) -> requests.Response:
    """Make HTTP request with optional authentication."""
    url = f"{BASE_URL}{endpoint}"
    headers = {}

    if use_auth:
        headers["Authorization"] = f"Bearer {TEST_API_KEY}"

    if method == "GET":
        response = requests.get(url, headers=headers, params=data)
    elif method == "POST":
        headers["Content-Type"] = "application/json"
        response = requests.post(url, headers=headers, json=data)
    else:
        raise ValueError(f"Unsupported method: {method}")

    return response


def test_conditional_auth_endpoints(require_auth: bool):
    """Test endpoints that should respect REQUIRE_AUTH setting."""
    print(f"\n=== Testing conditional auth endpoints (REQUIRE_AUTH={require_auth}) ===")

    for method, endpoint, data in CONDITIONAL_AUTH_ENDPOINTS:
        print(f"\nTesting {method} {endpoint}")

        # Test without auth
        try:
            response = make_request(method, endpoint, data, use_auth=False)
            if require_auth:
                if response.status_code == 401:
                    print(f"  ✓ Without auth: {response.status_code} (expected 401)")
                else:
                    print(f"  ✗ Without auth: {response.status_code} (expected 401)")
            else:
                if response.status_code in [
                    200,
                    202,
                    400,
                    404,
                ]:  # 400/404 for invalid data is OK
                    print(f"  ✓ Without auth: {response.status_code} (auth disabled)")
                else:
                    print(
                        f"  ✗ Without auth: {response.status_code} (expected success when auth disabled)"
                    )
        except Exception as e:
            print(f"  ✗ Without auth: Error - {e}")

        # Test with auth
        try:
            response = make_request(method, endpoint, data, use_auth=True)
            if response.status_code in [
                200,
                202,
                400,
                404,
            ]:  # 400/404 for invalid data is OK
                print(f"  ✓ With auth: {response.status_code} (should work)")
            else:
                print(f"  ✗ With auth: {response.status_code} (expected success)")
        except Exception as e:
            print(f"  ✗ With auth: Error - {e}")


def test_no_auth_endpoints():
    """Test endpoints that should never require auth."""
    print("\n=== Testing no-auth endpoints ===")

    for method, endpoint, data in NO_AUTH_ENDPOINTS:
        print(f"\nTesting {method} {endpoint}")

        try:
            response = make_request(method, endpoint, data, use_auth=False)
            if response.status_code in [200, 202]:
                print(f"  ✓ Without auth: {response.status_code} (should always work)")
            else:
                print(f"  ✗ Without auth: {response.status_code} (expected success)")
        except Exception as e:
            print(f"  ✗ Without auth: Error - {e}")


def test_always_auth_endpoints():
    """Test endpoints that should always require auth."""
    print("\n=== Testing always-auth endpoints ===")

    for method, endpoint, data in ALWAYS_AUTH_ENDPOINTS:
        print(f"\nTesting {method} {endpoint}")

        # Test without auth
        try:
            response = make_request(method, endpoint, data, use_auth=False)
            if response.status_code == 401:
                print(f"  ✓ Without auth: {response.status_code} (expected 401)")
            else:
                print(f"  ✗ Without auth: {response.status_code} (expected 401)")
        except Exception as e:
            print(f"  ✗ Without auth: Error - {e}")

        # Test with auth
        try:
            response = make_request(method, endpoint, data, use_auth=True)
            if response.status_code in [200, 202]:
                print(f"  ✓ With auth: {response.status_code} (should work)")
            else:
                print(f"  ✗ With auth: {response.status_code} (expected success)")
        except Exception as e:
            print(f"  ✗ With auth: Error - {e}")


def main():
    """Main test function."""
    print("=== Conditional Authentication Test ===")
    print(f"Testing server at: {BASE_URL}")
    print(f"Using API key: {TEST_API_KEY}")

    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/ping")
        if response.status_code != 200:
            print(f"Server not responding correctly: {response.status_code}")
            sys.exit(1)
    except Exception as e:
        print(f"Cannot connect to server: {e}")
        sys.exit(1)

    print("Server is running ✓")

    # Test no-auth endpoints (should always work)
    test_no_auth_endpoints()

    # Test always-auth endpoints (should always require auth)
    test_always_auth_endpoints()

    # Test conditional auth endpoints with REQUIRE_AUTH=true
    print(f"\n{'=' * 60}")
    print(
        "NOTE: To test REQUIRE_AUTH=false, set REQUIRE_AUTH=false in .env and restart server"
    )
    print("Then run this script again to see the difference")
    print(f"{'=' * 60}")

    # Test assuming REQUIRE_AUTH=true (default)
    test_conditional_auth_endpoints(require_auth=True)

    print("\n=== Test completed ===")


if __name__ == "__main__":
    main()
