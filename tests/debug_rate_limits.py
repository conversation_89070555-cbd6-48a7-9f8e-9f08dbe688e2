#!/usr/bin/env python3
"""
Debug script to check rate limiting configuration.
"""

from api.middleware.rate_limiting import (
    AdvancedRateLimitingMiddleware,
    EndpointLimits,
    RateLimitConfig,
    UserLimits,
)
from core.config import get_settings


def debug_rate_limits():
    """Debug rate limiting configuration."""
    print("=== Rate Limiting Debug ===")

    # Check settings
    settings = get_settings()
    print(f"RATE_LIMITING_ENABLED: {settings.RATE_LIMITING_ENABLED}")
    print(f"CONVERT_RATE_LIMIT_MINUTE: {settings.CONVERT_RATE_LIMIT_MINUTE}")
    print(f"CONVERT_RATE_LIMIT_HOUR: {settings.CONVERT_RATE_LIMIT_HOUR}")
    print(f"CONVERT_RATE_LIMIT_DAY: {settings.CONVERT_RATE_LIMIT_DAY}")
    print()

    # Check default endpoint limits
    endpoint_limits = EndpointLimits()
    print("=== Default Endpoint Limits ===")
    print(
        f"Convert: {endpoint_limits.convert.requests_per_minute}/min, {endpoint_limits.convert.burst_limit} burst"
    )
    print(
        f"Subtitles: {endpoint_limits.subtitles.requests_per_minute}/min, {endpoint_limits.subtitles.burst_limit} burst"
    )
    print(
        f"Summarize: {endpoint_limits.summarize.requests_per_minute}/min, {endpoint_limits.summarize.burst_limit} burst"
    )
    print()

    # Check configured endpoint limits (as in app.py)
    endpoint_limits_configured = EndpointLimits()
    endpoint_limits_configured.convert = RateLimitConfig(
        requests_per_minute=settings.CONVERT_RATE_LIMIT_MINUTE,
        requests_per_hour=settings.CONVERT_RATE_LIMIT_HOUR,
        requests_per_day=settings.CONVERT_RATE_LIMIT_DAY,
        burst_limit=20,
    )
    endpoint_limits_configured.subtitles = RateLimitConfig(
        requests_per_minute=settings.SUBTITLES_RATE_LIMIT_MINUTE,
        requests_per_hour=settings.SUBTITLES_RATE_LIMIT_HOUR,
        requests_per_day=settings.SUBTITLES_RATE_LIMIT_DAY,
        burst_limit=3,
    )

    print("=== Configured Endpoint Limits (as in app.py) ===")
    print(
        f"Convert: {endpoint_limits_configured.convert.requests_per_minute}/min, {endpoint_limits_configured.convert.burst_limit} burst"
    )
    print(
        f"Subtitles: {endpoint_limits_configured.subtitles.requests_per_minute}/min, {endpoint_limits_configured.subtitles.burst_limit} burst"
    )
    print()

    # Check user limits
    user_limits = UserLimits()
    print("=== Default User Limits ===")
    print(
        f"Anonymous: {user_limits.anonymous.requests_per_minute}/min, {user_limits.anonymous.burst_limit} burst"
    )
    print(
        f"Read-only: {user_limits.read_only.requests_per_minute}/min, {user_limits.read_only.burst_limit} burst"
    )
    print(
        f"Read-write: {user_limits.read_write.requests_per_minute}/min, {user_limits.read_write.burst_limit} burst"
    )
    print(
        f"Admin: {user_limits.admin.requests_per_minute}/min, {user_limits.admin.burst_limit} burst"
    )
    print()

    # Test middleware logic
    middleware = AdvancedRateLimitingMiddleware(
        app=None,
        user_limits=user_limits,
        endpoint_limits=endpoint_limits_configured,
        enabled=True,
    )

    print("=== Middleware Endpoint Detection ===")
    test_paths = [
        "/api/convert/ttml-to-txt",
        "/api/subtitles/get_id",
        "/api/summarize",
        "/api/tasks/123",
    ]

    for path in test_paths:
        endpoint_config = middleware._get_endpoint_config(path)
        print(
            f"{path:<30} -> {endpoint_config.requests_per_minute}/min, {endpoint_config.burst_limit} burst"
        )

    print()

    # Test combined config with auth enabled
    print("=== Combined Config with REQUIRE_AUTH=true ===")
    for path in test_paths:
        combined_config = middleware._get_rate_limit_config(
            path, []
        )  # Anonymous user (no permissions)
        print(
            f"{path:<30} -> {combined_config.requests_per_minute}/min, {combined_config.burst_limit} burst"
        )

    print()

    # Test with auth disabled
    print("=== Testing with REQUIRE_AUTH=false ===")
    original_require_auth = settings.REQUIRE_AUTH
    settings.REQUIRE_AUTH = False

    middleware_no_auth = AdvancedRateLimitingMiddleware(
        app=None,
        user_limits=user_limits,
        endpoint_limits=endpoint_limits_configured,
        enabled=True,
    )

    print("Combined Config with REQUIRE_AUTH=false:")
    for path in test_paths:
        combined_config = middleware_no_auth._get_rate_limit_config(path, [])
        print(
            f"{path:<30} -> {combined_config.requests_per_minute}/min, {combined_config.burst_limit} burst"
        )

    # Restore original setting
    settings.REQUIRE_AUTH = original_require_auth

    print()
    print("=== Analysis ===")
    convert_endpoint_config = middleware._get_endpoint_config(
        "/api/convert/ttml-to-txt"
    )
    convert_combined_config_auth = middleware._get_rate_limit_config(
        "/api/convert/ttml-to-txt", []
    )
    convert_combined_config_no_auth = middleware_no_auth._get_rate_limit_config(
        "/api/convert/ttml-to-txt", []
    )

    print(f"Current REQUIRE_AUTH setting: {settings.REQUIRE_AUTH}")
    print(f"Convert endpoint config: {convert_endpoint_config.requests_per_minute}/min")
    print(f"Anonymous user config: {user_limits.anonymous.requests_per_minute}/min")
    print(
        f"Combined config (REQUIRE_AUTH=true): {convert_combined_config_auth.requests_per_minute}/min"
    )
    print(
        f"Combined config (REQUIRE_AUTH=false): {convert_combined_config_no_auth.requests_per_minute}/min"
    )

    if (
        settings.REQUIRE_AUTH
        and convert_combined_config_auth.requests_per_minute
        < convert_endpoint_config.requests_per_minute
    ):
        print(
            "⚠️  With auth enabled: User limits are more restrictive than endpoint limits!"
        )
    elif not settings.REQUIRE_AUTH:
        print(
            "✅ With auth disabled: Using endpoint limits only - converters get full throughput!"
        )
    else:
        print("✅ With auth enabled: Endpoint limits are being used properly!")


if __name__ == "__main__":
    debug_rate_limits()
