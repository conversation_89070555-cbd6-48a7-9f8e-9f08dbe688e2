#!/usr/bin/env python3
"""
Test script for converter rate limits.

This script tests the new high-throughput rate limits for converter endpoints.
"""

import asyncio
import time

import aiohttp
from loguru import logger

# Configuration
BASE_URL = "http://localhost:8000"
CONVERT_ENDPOINT = "/api/convert/ttml-to-txt"
API_KEY = "ak_test_key_123"  # Replace with actual API key if auth is enabled

# Test TTML content
TEST_TTML = """<?xml version="1.0" encoding="utf-8"?>
<tt xmlns="http://www.w3.org/ns/ttml">
    <body>
        <div>
            <p begin="00:00:01.000" end="00:00:03.000">Hello world</p>
            <p begin="00:00:04.000" end="00:00:06.000">This is a test</p>
        </div>
    </body>
</tt>"""


async def make_convert_request(
    session: aiohttp.ClientSession, request_id: int
) -> tuple[int, int, float]:
    """Make a single convert request and return status code, request_id, and response time."""
    start_time = time.time()

    headers = {"Content-Type": "application/json", "Authorization": f"Bearer {API_KEY}"}

    payload = {"ttml_text": TEST_TTML}

    try:
        async with session.post(
            f"{BASE_URL}{CONVERT_ENDPOINT}",
            json=payload,
            headers=headers,
            timeout=aiohttp.ClientTimeout(total=10),
        ) as response:
            response_time = time.time() - start_time
            await response.text()  # Read response body
            return response.status, request_id, response_time
    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"Request {request_id} failed: {e}")
        return 0, request_id, response_time


async def test_rate_limits(num_requests: int = 50, concurrent_requests: int = 10):
    """Test converter rate limits with multiple concurrent requests."""
    logger.info(
        f"Testing converter rate limits with {num_requests} requests, {concurrent_requests} concurrent"
    )

    async with aiohttp.ClientSession() as session:
        # Test server availability first
        try:
            async with session.get(f"{BASE_URL}/ping") as response:
                if response.status != 200:
                    logger.error(f"Server not available: {response.status}")
                    return
        except Exception as e:
            logger.error(f"Cannot connect to server: {e}")
            return

        # Run requests in batches
        results = []
        start_time = time.time()

        for batch_start in range(0, num_requests, concurrent_requests):
            batch_end = min(batch_start + concurrent_requests, num_requests)
            batch_tasks = []

            for i in range(batch_start, batch_end):
                task = make_convert_request(session, i + 1)
                batch_tasks.append(task)

            # Execute batch concurrently
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

            for result in batch_results:
                if isinstance(result, tuple):
                    results.append(result)
                else:
                    logger.error(f"Batch request failed: {result}")

            # Small delay between batches to avoid overwhelming the server
            await asyncio.sleep(0.1)

        total_time = time.time() - start_time

        # Analyze results
        successful_requests = [r for r in results if r[0] == 200]
        rate_limited_requests = [r for r in results if r[0] == 429]
        failed_requests = [r for r in results if r[0] not in [200, 429]]

        avg_response_time = (
            sum(r[2] for r in successful_requests) / len(successful_requests)
            if successful_requests
            else 0
        )

        logger.info("=== Rate Limit Test Results ===")
        logger.info(f"Total requests: {len(results)}")
        logger.info(f"Successful (200): {len(successful_requests)}")
        logger.info(f"Rate limited (429): {len(rate_limited_requests)}")
        logger.info(f"Failed (other): {len(failed_requests)}")
        logger.info(f"Total time: {total_time:.2f}s")
        logger.info(f"Requests per second: {len(results) / total_time:.2f}")
        logger.info(f"Average response time: {avg_response_time:.3f}s")

        if rate_limited_requests:
            logger.warning(
                f"Rate limiting triggered after {len(successful_requests)} successful requests"
            )
            logger.info("This is expected behavior when testing rate limits")
        else:
            logger.success(
                "All requests completed successfully - rate limits are generous enough"
            )

        return {
            "total": len(results),
            "successful": len(successful_requests),
            "rate_limited": len(rate_limited_requests),
            "failed": len(failed_requests),
            "total_time": total_time,
            "rps": len(results) / total_time,
            "avg_response_time": avg_response_time,
        }


async def test_burst_limits():
    """Test burst limits with rapid requests."""
    logger.info("Testing burst limits with rapid requests")

    async with aiohttp.ClientSession() as session:
        # Send 25 requests as fast as possible (burst limit is 20)
        tasks = []
        for i in range(25):
            task = make_convert_request(session, i + 1)
            tasks.append(task)

        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time

        valid_results = [r for r in results if isinstance(r, tuple)]
        successful = [r for r in valid_results if r[0] == 200]
        rate_limited = [r for r in valid_results if r[0] == 429]

        logger.info("=== Burst Limit Test Results ===")
        logger.info(f"Total requests: {len(valid_results)}")
        logger.info(f"Successful (200): {len(successful)}")
        logger.info(f"Rate limited (429): {len(rate_limited)}")
        logger.info(f"Total time: {total_time:.2f}s")
        logger.info(f"Requests per second: {len(valid_results) / total_time:.2f}")


if __name__ == "__main__":
    logger.info("Starting converter rate limit tests")

    # Test normal rate limits
    asyncio.run(test_rate_limits(num_requests=30, concurrent_requests=5))

    # Wait a bit
    time.sleep(2)

    # Test burst limits
    asyncio.run(test_burst_limits())

    logger.info("Rate limit tests completed")
