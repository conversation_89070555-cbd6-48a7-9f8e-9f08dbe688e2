#!/usr/bin/env python3
"""
Test script for client performance with new parallel processing.
"""

import asyncio
import tempfile
import time
from pathlib import Path

# Test TTML content
TEST_TTML_CONTENT = """<?xml version="1.0" encoding="utf-8"?>
<tt xmlns="http://www.w3.org/ns/ttml">
    <body>
        <div>
            <p begin="00:00:01.000" end="00:00:03.000">Hello world {file_num}</p>
            <p begin="00:00:04.000" end="00:00:06.000">This is test file number {file_num}</p>
        </div>
    </body>
</tt>"""


def create_test_files(directory: Path, num_files: int = 50):
    """Create test TTML files for performance testing."""
    print(f"Creating {num_files} test TTML files in {directory}")

    for i in range(num_files):
        file_path = directory / f"test_file_{i:03d}.ttml"
        content = TEST_TTML_CONTENT.format(file_num=i)
        file_path.write_text(content, encoding="utf-8")

    print(f"Created {num_files} test files")


async def test_client_performance():
    """Test client performance with different concurrency levels."""

    # Import client modules
    import sys

    sys.path.append(str(Path(__file__).parent / "client" / "utils"))

    from convert_ttml_files import ApiClient, TtmlFileProcessor

    # Create temporary directory with test files
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        num_files = 50
        create_test_files(temp_path, num_files)

        # Test different concurrency levels
        concurrency_levels = [1, 5, 10, 20]

        for concurrent in concurrency_levels:
            print(f"\n=== Testing with {concurrent} concurrent requests ===")

            # Create API client
            api_client = ApiClient(debug=False)

            # Check if server is available
            if not await api_client.check_server_available():
                print("❌ Server not available, skipping test")
                continue

            # Create file processor
            processor = TtmlFileProcessor(temp_path, api_client, debug=False)

            # Measure processing time
            start_time = time.time()

            try:
                await processor.process_directory(max_concurrent=concurrent)

                end_time = time.time()
                duration = end_time - start_time
                files_per_second = num_files / duration if duration > 0 else 0

                print(f"✅ Processed {num_files} files in {duration:.2f}s")
                print(f"   Rate: {files_per_second:.2f} files/second")
                print(f"   Concurrency: {concurrent}")

                # Clean up generated .txt files for next test
                for txt_file in temp_path.glob("*.txt"):
                    txt_file.unlink()

            except Exception as e:
                print(f"❌ Error with concurrency {concurrent}: {e}")

            # Wait between tests
            await asyncio.sleep(2)


if __name__ == "__main__":
    print("Testing client performance with new parallel processing...")
    asyncio.run(test_client_performance())
