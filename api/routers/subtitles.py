"""
Subtitle extraction endpoints.
"""

import asyncio
import builtins
from typing import Any

from fastapi import APIRouter, Depends, Request
from fastapi.responses import JSONResponse
from loguru import logger

from api.middleware.auth import require_write_conditional
from api.middleware.error_handler import (
    ServiceUnavailableError,
    TimeoutError,
    ValidationError,
)
from core.config import get_settings
from models.schemas import (
    SubtitleRequest,
    SubtitleResponse,
    TaskStatus,
    VideoListRequest,
    VideoListResponse,
)

router = APIRouter()


def _check_server_overload(task_queue):
    """Check if server is overloaded and raise appropriate error."""
    active_tasks_count = len(task_queue.active_async_tasks)
    max_total_workers = (
        task_queue.LIMIT_SUBTITLE_WORKERS + task_queue.LIMIT_SUMMARIZE_WORKERS
    )
    is_subtitle_queue_full = task_queue.task_manager.queues["subtitle"].full()

    if is_subtitle_queue_full:
        raise ServiceUnavailableError(
            "Subtitle task queue is full. Please try again later."
        )

    if active_tasks_count >= max_total_workers:
        raise ServiceUnavailableError(
            "Server is at maximum processing capacity. Please try again later."
        )


@router.post("/subtitles/get_id", response_model=VideoListResponse)
async def get_video_ids(
    request: VideoListRequest,
    req: Request,
    user: dict[str, Any] = Depends(require_write_conditional),
):
    """
    Submit a YouTube URL to get list of video IDs.

    By default, this endpoint processes requests synchronously for fast response (1-5 seconds).
    This behavior can be controlled via ENABLE_SYNC_VIDEO_LIST environment variable.
    Set ENABLE_SYNC_VIDEO_LIST=false to force async processing via task queue.

    Requires: write permission
    """
    settings = get_settings()
    task_queue = req.app.state.task_queue

    # Note: client_uid is deprecated and ignored in processing
    if request.client_uid:
        logger.debug(
            f"Received deprecated client_uid: {request.client_uid} (ignored for processing)"
        )

    logger.debug(
        f"Received /subtitles/get_id request for URL: {request.url}, sync_enabled={settings.ENABLE_SYNC_VIDEO_LIST}"
    )

    # Check server overload (will raise ServiceUnavailableError if overloaded)
    _check_server_overload(task_queue)

    # Use local variable for sync mode to allow fallback
    sync_mode = settings.ENABLE_SYNC_VIDEO_LIST

    if sync_mode:
        # Synchronous processing for fast response
        try:
            logger.debug(
                f"Processing video list extraction synchronously for URL: {request.url}"
            )

            # Get the downloader from task queue
            downloader = task_queue.task_manager.task_handler.downloader
            if not downloader:
                logger.error("Downloader not initialized")
                raise ServiceUnavailableError("Service temporarily unavailable")

            # Extract video IDs synchronously with timeout
            video_ids = await asyncio.wait_for(
                asyncio.get_running_loop().run_in_executor(
                    None, downloader.extract_video_ids, str(request.url)
                ),
                timeout=10.0,  # 10 second timeout for sync processing
            )

            logger.debug(
                f"Extracted {len(video_ids)} video IDs synchronously for URL: {request.url}"
            )

            # Create response object
            response = VideoListResponse(
                task_id=f"sync_{int(asyncio.get_event_loop().time() * 1000)}",  # Unique sync task ID
                status=TaskStatus.COMPLETED,
                video_ids=video_ids,
                error=None,
            )

            return JSONResponse(status_code=200, content=response.model_dump())

        except builtins.TimeoutError:
            logger.warning(
                f"Sync processing timeout for URL: {request.url}, falling back to async"
            )
            # Fall back to async processing
            sync_mode = False
        except Exception as e:
            logger.error(
                f"Error in sync processing for URL: {request.url}: {str(e)}",
                exc_info=True,
            )
            # For other errors, try async processing as fallback
            sync_mode = False

    if not sync_mode:
        # Asynchronous processing via task queue (original behavior)
        try:
            logger.debug(
                f"Attempting to add task for {request.url} to video_list_task (via subtitle queue)."
            )
            response = await asyncio.wait_for(
                task_queue.add_video_list_task(
                    url=str(request.url),
                    client_uid=None,  # Explicitly set to None - deprecated field
                ),
                timeout=5.0,
            )
            logger.debug(
                f"Video list task added for {request.url}. Response from add_video_list_task: ID {response.task_id}, Status {response.status}"
            )
            status_code = 200 if response.status == TaskStatus.COMPLETED else 202
            return JSONResponse(status_code=status_code, content=response.model_dump())

        except builtins.TimeoutError:
            logger.error(
                f"Timeout adding video list task to queue for URL: {request.url}",
                exc_info=True,
            )
            raise TimeoutError("Server is busy, please try again later")

        except ValueError as e:
            # Invalid YouTube URLs
            raise ValidationError(str(e))


@router.post("/subtitles/get_id_async", response_model=VideoListResponse)
async def get_video_ids_async(
    request: VideoListRequest,
    req: Request,
    user: dict[str, Any] = Depends(require_write_conditional),
):
    """
    Submit a YouTube URL to get list of video IDs (asynchronous processing).

    This endpoint always uses asynchronous processing via the task queue.
    Use this for large playlists or when you prefer async workflow.

    Requires: write permission
    """
    task_queue = req.app.state.task_queue

    # Note: client_uid is deprecated and ignored in processing
    if request.client_uid:
        logger.debug(
            f"Received deprecated client_uid: {request.client_uid} (ignored for processing)"
        )

    logger.debug(f"Received /subtitles/get_id_async request for URL: {request.url}")

    # Check server overload (will raise ServiceUnavailableError if overloaded)
    _check_server_overload(task_queue)

    try:
        logger.debug(
            f"Attempting to add task for {request.url} to video_list_task (via subtitle queue)."
        )
        response = await asyncio.wait_for(
            task_queue.add_video_list_task(
                url=str(request.url),
                client_uid=None,  # Explicitly set to None - deprecated field
            ),
            timeout=5.0,
        )
        logger.debug(
            f"Video list task added for {request.url}. Response from add_video_list_task: ID {response.task_id}, Status {response.status}"
        )
        status_code = 200 if response.status == TaskStatus.COMPLETED else 202
        return JSONResponse(status_code=status_code, content=response.model_dump())

    except builtins.TimeoutError:
        logger.error(
            f"Timeout adding video list task to queue for URL: {request.url}",
            exc_info=True,
        )
        raise TimeoutError("Server is busy, please try again later")

    except ValueError as e:
        # Invalid YouTube URLs
        raise ValidationError(str(e))


@router.post("/subtitles", response_model=SubtitleResponse)
async def create_subtitles_task(
    request: SubtitleRequest,
    req: Request,
    user: dict[str, Any] = Depends(require_write_conditional),
):
    """
    Submit a new YouTube URL for subtitle extraction.

    Requires: write permission
    """
    task_queue = req.app.state.task_queue

    # Note: client_uid is deprecated and ignored in processing
    if request.client_uid:
        logger.debug(
            f"Received deprecated client_uid: {request.client_uid} (ignored for processing)"
        )

    logger.debug(f"Received /subtitles request for URL: {request.url}")

    # Check server overload (will raise ServiceUnavailableError if overloaded)
    _check_server_overload(task_queue)

    try:
        logger.debug(
            f"Attempting to add task for {request.url} to subtitle task queue."
        )
        response = await asyncio.wait_for(
            task_queue.add_subtitle_task(
                video_id=str(request.url),
                url=str(request.url),
                client_uid=None,  # Explicitly set to None - deprecated field
            ),
            timeout=5.0,
        )
        logger.debug(
            f"Subtitle task added for {request.url}. Response from add_subtitle_task: ID {response.task_id}, Status {response.status}"
        )
        status_code = 200 if response.status == TaskStatus.COMPLETED else 202
        return JSONResponse(status_code=status_code, content=response.model_dump())

    except builtins.TimeoutError:
        logger.error(
            f"Timeout adding subtitle task to queue for URL: {request.url}",
            exc_info=True,
        )
        raise TimeoutError("Server is busy, please try again later")

    except ValueError as e:
        # Invalid YouTube URLs
        raise ValidationError(str(e))
