"""
Task management endpoints.
"""

from typing import Any

from fastapi import APIRouter, Depends, Request
from loguru import logger

from api.middleware.auth import require_read_conditional
from api.middleware.error_handler import NotFoundError
from models.schemas import SubtitleResponse, SummarizeResponse, VideoListResponse

router = APIRouter()


@router.get(
    "/task/{task_id}",
    response_model=SubtitleResponse | SummarizeResponse | VideoListResponse,
)
async def get_task(
    task_id: str,
    request: Request,
    user: dict[str, Any] = Depends(require_read_conditional),
):
    """
    Get the status or result of any task (subtitles, summarization, or video list).

    Requires: read permission
    """
    task_queue = request.app.state.task_queue

    logger.debug(f"Received request to get task status for task_id: {task_id}")

    try:
        # Check if this is a sync task ID (starts with "sync_")
        if task_id.startswith("sync_"):
            logger.debug(f"Sync task ID detected: {task_id}")
            # Sync tasks are completed immediately and not stored in task manager
            # Return a generic "completed but not found" response
            raise NotFoundError(
                f"Sync task '{task_id}' was completed immediately and is no longer tracked"
            )

        # get_task is a synchronous method, not async
        task = task_queue.get_task(task_id)
        if task is None:
            logger.debug(f"Task not found: {task_id}")
            raise NotFoundError(f"Task with ID '{task_id}' not found")

        logger.debug(f"Task found: {task_id}, status: {task.status}")
        return task

    except NotFoundError:
        # Re-raise NotFoundError as-is
        raise
    except Exception as e:
        logger.error(f"Error retrieving task {task_id}: {e}", exc_info=True)
        raise NotFoundError(f"Task with ID '{task_id}' not found")
