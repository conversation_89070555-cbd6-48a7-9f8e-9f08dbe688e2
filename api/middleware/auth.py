"""
Authentication middleware and utilities.
"""

import secrets
import time
from typing import Any

from fastapi import Depends, Request, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from loguru import logger

from api.middleware.error_handler import APIError, ErrorCode
from core.config import get_settings

settings = get_settings()
security = HTTPBearer(auto_error=False)


class AuthError(APIError):
    """Authentication error."""

    def __init__(self, message: str = "Authentication failed"):
        super().__init__(
            message=message,
            error_code=ErrorCode.UNAUTHORIZED,
            status_code=status.HTTP_401_UNAUTHORIZED,
        )


class ForbiddenError(APIError):
    """Authorization error."""

    def __init__(self, message: str = "Access forbidden"):
        super().__init__(
            message=message,
            error_code=ErrorCode.FORBIDDEN,
            status_code=status.HTTP_403_FORBIDDEN,
        )


class APIKeyManager:
    """Manages API keys and authentication."""

    def __init__(self):
        # In production, these should be stored in a database
        # For now, we'll use environment variables and in-memory storage
        self._api_keys: dict[str, dict[str, Any]] = {}
        self._load_default_keys()

    def _load_default_keys(self):
        """Load default API keys from environment or create demo keys."""
        # Load from environment if available
        if hasattr(settings, "API_KEYS") and settings.API_KEYS:
            for key_data in settings.API_KEYS:
                self._api_keys[key_data["key"]] = {
                    "name": key_data.get("name", "Unknown"),
                    "permissions": key_data.get("permissions", ["read", "write"]),
                    "rate_limit": key_data.get("rate_limit", 100),  # requests per hour
                    "active": key_data.get("active", True),
                }
        else:
            # Create demo keys for development
            demo_key = "demo_key_12345"
            admin_key = "admin_key_67890"

            self._api_keys[demo_key] = {
                "name": "Demo User",
                "permissions": ["read"],
                "rate_limit": 50,
                "active": True,
            }

            self._api_keys[admin_key] = {
                "name": "Admin User",
                "permissions": ["read", "write", "admin"],
                "rate_limit": 1000,
                "active": True,
            }

            logger.debug(
                f"Created demo API keys: {demo_key} (read-only), {admin_key} (admin)"
            )

    def validate_api_key(self, api_key: str) -> dict[str, Any] | None:
        """Validate an API key and return user info."""
        if not api_key:
            return None

        key_info = self._api_keys.get(api_key)
        if not key_info or not key_info.get("active", False):
            return None

        return key_info

    def generate_api_key(
        self, name: str, permissions: list = None, rate_limit: int = 100
    ) -> str:
        """Generate a new API key."""
        if permissions is None:
            permissions = ["read"]

        # Generate a secure random key
        api_key = f"ak_{secrets.token_urlsafe(32)}"

        self._api_keys[api_key] = {
            "name": name,
            "permissions": permissions,
            "rate_limit": rate_limit,
            "active": True,
        }

        logger.info(f"Generated new API key for {name}: {api_key[:10]}...")
        return api_key

    def revoke_api_key(self, api_key: str) -> bool:
        """Revoke an API key."""
        if api_key in self._api_keys:
            self._api_keys[api_key]["active"] = False
            logger.info(f"Revoked API key: {api_key[:10]}...")
            return True
        return False

    def list_api_keys(self) -> dict[str, dict[str, Any]]:
        """List all API keys (without exposing the full key)."""
        return {
            f"{key[:10]}...": {
                "name": info["name"],
                "permissions": info["permissions"],
                "rate_limit": info["rate_limit"],
                "active": info["active"],
            }
            for key, info in self._api_keys.items()
        }


# Global API key manager
api_key_manager = APIKeyManager()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> dict[str, Any]:
    """
    Get current authenticated user from API key.

    Args:
        credentials: HTTP Bearer token credentials

    Returns:
        User information dictionary

    Raises:
        AuthError: If authentication fails
    """
    if not credentials:
        raise AuthError("API key required")

    api_key = credentials.credentials
    user_info = api_key_manager.validate_api_key(api_key)

    if not user_info:
        logger.warning(f"Invalid API key attempt: {api_key[:10]}...")
        raise AuthError("Invalid API key")

    logger.debug(f"Authenticated user: {user_info['name']}")
    return {
        "api_key": api_key,
        "name": user_info["name"],
        "permissions": user_info["permissions"],
        "rate_limit": user_info["rate_limit"],
    }


async def get_current_user_conditional(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> dict[str, Any] | None:
    """
    Get current authenticated user from API key, but only if authentication is required.

    If REQUIRE_AUTH=false, returns a default anonymous user with full permissions.
    If REQUIRE_AUTH=true, requires valid authentication.

    Args:
        credentials: HTTP Bearer token credentials

    Returns:
        User information dictionary or anonymous user when auth is disabled

    Raises:
        AuthError: If authentication is required but fails
    """
    from core.config import get_settings

    settings = get_settings()

    # If authentication is disabled, return anonymous user
    if not settings.REQUIRE_AUTH:
        logger.debug(
            "Authentication disabled (REQUIRE_AUTH=false), using anonymous user"
        )
        return {
            "name": "anonymous",
            "permissions": [
                "read",
                "write",
                "admin",
            ],  # Full permissions when auth disabled
            "rate_limit": 1000,
            "auth_method": "disabled",
        }

    # Authentication is required, use normal flow
    return await get_current_user(credentials)


async def get_optional_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> dict[str, Any] | None:
    """
    Get current user if authenticated, otherwise return None.
    Used for endpoints that have optional authentication.
    """
    if not credentials:
        return None

    try:
        return await get_current_user(credentials)
    except AuthError:
        return None


def require_permissions(*required_permissions: str):
    """
    Decorator to require specific permissions.

    Args:
        required_permissions: List of required permissions

    Returns:
        Dependency function that checks permissions
    """

    async def check_permissions(
        user: dict[str, Any] = Depends(get_current_user),
    ) -> dict[str, Any]:
        user_permissions = user.get("permissions", [])

        # Check if user has all required permissions
        missing_permissions = [
            perm for perm in required_permissions if perm not in user_permissions
        ]

        if missing_permissions:
            logger.warning(
                f"User {user['name']} missing permissions: {missing_permissions}. "
                f"Required: {required_permissions}, Has: {user_permissions}"
            )
            raise ForbiddenError(f"Missing required permissions: {missing_permissions}")

        return user

    return check_permissions


def require_permissions_conditional(*required_permissions: str):
    """
    Decorator to require specific permissions, but respects REQUIRE_AUTH setting.

    If REQUIRE_AUTH=false, returns anonymous user with full permissions.
    If REQUIRE_AUTH=true, requires valid authentication and permissions.

    Args:
        required_permissions: List of required permissions

    Returns:
        Dependency function that checks permissions conditionally
    """

    async def check_permissions_conditional(
        user: dict[str, Any] | None = Depends(get_current_user_conditional),
    ) -> dict[str, Any]:
        # user will be None only if auth is disabled and we get anonymous user
        if user is None:
            # This shouldn't happen with get_current_user_conditional, but just in case
            from core.config import get_settings

            settings = get_settings()
            if not settings.REQUIRE_AUTH:
                return {
                    "name": "anonymous",
                    "permissions": ["read", "write", "admin"],
                    "rate_limit": 1000,
                    "auth_method": "disabled",
                }
            else:
                raise AuthError("Authentication required")

        # If auth is disabled, user already has full permissions
        if user.get("auth_method") == "disabled":
            return user

        # Normal permission checking for authenticated users
        user_permissions = user.get("permissions", [])

        # Check if user has all required permissions
        missing_permissions = [
            perm for perm in required_permissions if perm not in user_permissions
        ]

        if missing_permissions:
            logger.warning(
                f"User {user['name']} missing permissions: {missing_permissions}. "
                f"Required: {required_permissions}, Has: {user_permissions}"
            )
            raise ForbiddenError(f"Missing required permissions: {missing_permissions}")

        return user

    return check_permissions_conditional


# Common permission dependencies
require_read = require_permissions("read")
require_write = require_permissions("write")
require_admin = require_permissions("admin")

# Conditional permission dependencies (respect REQUIRE_AUTH setting)
require_read_conditional = require_permissions_conditional("read")
require_write_conditional = require_permissions_conditional("write")
require_admin_conditional = require_permissions_conditional("admin")


async def get_websocket_user(websocket) -> dict[str, Any] | None:
    """
    Extract and validate user from WebSocket connection.

    Supports multiple authentication methods:
    1. Query parameter: ?token=api_key
    2. Authorization header: Authorization: Bearer api_key
    3. Subprotocol: Sec-WebSocket-Protocol: api_key

    Args:
        websocket: WebSocket connection object

    Returns:
        User information dictionary or None if not authenticated

    Raises:
        AuthError: If authentication is required but fails
    """
    from core.config import get_settings

    settings = get_settings()

    # If authentication is disabled, return None (no auth required)
    if not settings.REQUIRE_AUTH:
        logger.debug(
            "Authentication disabled (REQUIRE_AUTH=false), allowing WebSocket connection"
        )
        return None

    api_key = None
    auth_method = None

    # Method 1: Query parameter ?token=api_key
    if "token" in websocket.query_params:
        api_key = websocket.query_params["token"]
        auth_method = "query_param"

    # Method 2: Authorization header (if available in WebSocket headers)
    elif "authorization" in websocket.headers:
        auth_header = websocket.headers["authorization"]
        if auth_header.startswith("Bearer "):
            api_key = auth_header[7:]  # Remove "Bearer " prefix
            auth_method = "header"

    # Method 3: Subprotocol (Sec-WebSocket-Protocol header)
    elif websocket.headers.get("sec-websocket-protocol"):
        # Client can send API key as subprotocol
        protocols = websocket.headers["sec-websocket-protocol"].split(", ")
        for protocol in protocols:
            if protocol.startswith("api_key_"):
                api_key = protocol[8:]  # Remove "api_key_" prefix
                auth_method = "subprotocol"
                break

    if not api_key:
        logger.warning(
            f"WebSocket authentication required but no API key provided from {websocket.client}"
        )
        raise AuthError("API key required for WebSocket connection")

    # Validate API key
    user_info = api_key_manager.validate_api_key(api_key)
    if not user_info:
        logger.warning(
            f"Invalid API key attempt via WebSocket ({auth_method}): {api_key[:10]}... from {websocket.client}"
        )
        raise AuthError("Invalid API key")

    logger.debug(
        f"WebSocket authenticated via {auth_method}: {user_info['name']} from {websocket.client}"
    )
    return {
        "api_key": api_key,
        "name": user_info["name"],
        "permissions": user_info["permissions"],
        "rate_limit": user_info["rate_limit"],
        "auth_method": auth_method,
    }


async def get_optional_websocket_user(websocket) -> dict[str, Any] | None:
    """
    Get WebSocket user if authenticated, otherwise return None.
    Used for WebSocket endpoints that have optional authentication.
    """
    try:
        return await get_websocket_user(websocket)
    except AuthError:
        return None


async def require_websocket_permissions(
    websocket, *required_permissions: str
) -> dict[str, Any]:
    """
    WebSocket authentication function that requires specific permissions.

    Args:
        websocket: WebSocket connection object
        required_permissions: List of required permissions

    Returns:
        User information dictionary

    Raises:
        AuthError: If authentication fails
        ForbiddenError: If user lacks required permissions
    """
    user = await get_websocket_user(websocket)

    # If authentication is disabled, return a default user
    if user is None:
        return {
            "name": "anonymous",
            "permissions": ["read", "write"],  # Default permissions when auth disabled
            "rate_limit": 1000,
            "auth_method": "disabled",
        }

    user_permissions = user.get("permissions", [])

    # Check if user has all required permissions
    missing_permissions = [
        perm for perm in required_permissions if perm not in user_permissions
    ]

    if missing_permissions:
        logger.warning(
            f"WebSocket user {user['name']} missing permissions: {missing_permissions}. "
            f"Required: {required_permissions}, Has: {user_permissions}"
        )
        raise ForbiddenError(f"Missing required permissions: {missing_permissions}")

    return user


# WebSocket permission dependencies
async def require_websocket_read(websocket) -> dict[str, Any]:
    """Require read permission for WebSocket."""
    return await require_websocket_permissions(websocket, "read")


async def require_websocket_write(websocket) -> dict[str, Any]:
    """Require write permission for WebSocket."""
    return await require_websocket_permissions(websocket, "write")


async def require_websocket_admin(websocket) -> dict[str, Any]:
    """Require admin permission for WebSocket."""
    return await require_websocket_permissions(websocket, "admin")


async def auth_middleware(request: Request, call_next):
    """
    Authentication middleware that logs authentication attempts.
    """
    start_time = time.time()

    # Extract API key from Authorization header
    auth_header = request.headers.get("Authorization")
    api_key = None
    if auth_header and auth_header.startswith("Bearer "):
        api_key = auth_header[7:]  # Remove "Bearer " prefix

    # Add user info to request state if authenticated
    if api_key:
        user_info = api_key_manager.validate_api_key(api_key)
        if user_info:
            request.state.user = {
                "api_key": api_key,
                "name": user_info["name"],
                "permissions": user_info["permissions"],
                "rate_limit": user_info["rate_limit"],
            }

    response = await call_next(request)

    # Log authentication info with structured logging
    duration = time.time() - start_time
    user_info = getattr(request.state, "user", {})
    user_name = user_info.get("name", "Anonymous")

    # Use structured logging if available
    try:
        import structlog

        struct_logger = structlog.get_logger("api.middleware.auth")
        struct_logger.info(
            "auth_request_processed",
            method=request.method,
            path=request.url.path,
            user=user_name,
            user_id=user_info.get("id"),
            permissions=user_info.get("permissions", []),
            status_code=response.status_code,
            duration=round(duration, 4),
            authenticated=hasattr(request.state, "user") and bool(request.state.user),
        )
    except ImportError:
        # Fall back to loguru
        logger.info(
            "Request processed",
            extra={
                "method": request.method,
                "path": request.url.path,
                "user": user_name,
                "status_code": response.status_code,
                "duration": duration,
            },
        )

    return response
