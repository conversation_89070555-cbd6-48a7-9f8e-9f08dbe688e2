import argparse
import asyncio
import logging
import subprocess
import sys
from pathlib import Path

from rich.console import Console
from rich.progress import (
    BarColumn,
    Progress,
    SpinnerColumn,
    TaskProgressColumn,
    TextColumn,
)

# Конфигурация сервера по умолчанию
DEFAULT_HOST = "localhost"
DEFAULT_PORT = 8000

# Тестовый API токен для авторизации
TEST_API_TOKEN = "ak_s8049sqAdPdJ-brzIfR5A-6cofyP2b12o58ZGyLYEiE"

# Создаем логгер
logger = logging.getLogger(__name__)


class ApiClient:
    """Клиент для взаимодействия с API сервера конвертации TTML."""

    # Статическая переменная для эндпоинта конвертации TTML в TXT
    TTML_TO_TXT_ENDPOINT = "/api/convert/ttml-to-txt"

    def __init__(
        self,
        host: str = DEFAULT_HOST,
        port: int = DEFAULT_PORT,
        api_token: str = TEST_API_TOKEN,
        debug: bool = False,
    ):
        """Инициализация клиента API.

        Args:
            host: Хост сервера API
            port: Порт сервера API
            api_token: Токен для авторизации запросов к API
            debug: Режим отладки
        """
        self.debug = debug
        self._log_debug(f"ApiClient initializing with debug mode: {self.debug}")

        self.host = host
        self.port = port
        self.base_url = f"http://{self.host}:{self.port}"
        self.api_token = api_token
        self.console = Console()
        self._log_debug(f"ApiClient initialized. Host: {self.host}, Port: {self.port}")

    def _log_debug(self, message: str, exc_info: bool = False):
        """Логирование отладочных сообщений.

        Args:
            message: Сообщение для логирования
            exc_info: Включать ли информацию об исключении
        """
        if self.debug:
            logger.debug(message, exc_info=exc_info)

    async def check_server_available(self) -> bool:
        """Проверка доступности сервера через POST запрос к эндпоинту /ping

        Returns:
            bool: True, если сервер доступен, иначе False
        """
        self._log_debug("Checking server availability...")
        try:
            import aiohttp

            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.base_url}/ping", timeout=2) as response:
                    response_json = await response.json()
                    self._log_debug(
                        f"Server check response status: {response.status}, message: {response_json}"
                    )
                    return (
                        response.status == 200
                        and response_json.get("message") == "pong"
                    )
        except Exception as e:
            self._log_debug(f"Server check failed or server not running: {e}")
            self.console.print(  # This is a user-facing message, keep as console.print
                "[blue]Сторонний сервер не запущен. Запуск нового экземпляра FastAPI.[/blue]"
            )
            return False

    async def convert_ttml_to_txt(
        self, ttml_content: str, max_retries: int = 3
    ) -> tuple[bool, str | None, str | None]:
        """Отправка запроса на конвертацию TTML в TXT через API с повторными попытками.

        Args:
            ttml_content: Содержимое TTML файла
            max_retries: Максимальное количество повторных попыток при rate limiting

        Returns:
            Tuple[bool, Optional[str], Optional[str]]: (успех, текстовое содержимое, сообщение об ошибке)
        """
        import aiohttp

        for attempt in range(max_retries + 1):
            try:
                self._log_debug(
                    f"Sending TTML content to server for conversion (attempt {attempt + 1}/{max_retries + 1})"
                )

                async with aiohttp.ClientSession() as session:
                    # Подготовка данных запроса
                    request_body_dict = {"ttml_text": ttml_content}

                    # Добавляем заголовок авторизации
                    headers = {
                        "Authorization": f"Bearer {self.api_token}",
                        "Content-Type": "application/json",
                    }

                    self._log_debug(
                        f"Sending request with auth token to {self.base_url}{self.TTML_TO_TXT_ENDPOINT}"
                    )

                    async with session.post(
                        f"{self.base_url}{self.TTML_TO_TXT_ENDPOINT}",
                        json=request_body_dict,
                        headers=headers,
                        timeout=60,
                    ) as response:
                        # Успешный ответ
                        if response.status == 200:
                            response_data = await response.json()
                            txt_content = response_data.get("txt_content")

                            if not txt_content:
                                self._log_debug("Server response missing txt_content")
                                return False, None, "Отсутствует контент в ответе"

                            return True, txt_content, None

                        # Rate limiting - повторяем попытку
                        elif response.status == 429:
                            error_text = await response.text()
                            retry_after = int(response.headers.get("Retry-After", 1))

                            if attempt < max_retries:
                                self._log_debug(
                                    f"Rate limited (429), retrying after {retry_after}s (attempt {attempt + 1}/{max_retries + 1})"
                                )
                                await asyncio.sleep(retry_after)
                                continue
                            else:
                                self._log_debug(
                                    f"Rate limited after {max_retries + 1} attempts: {error_text}"
                                )
                                return (
                                    False,
                                    None,
                                    f"Rate limit exceeded after {max_retries + 1} attempts",
                                )

                        # Другие ошибки - не повторяем
                        else:
                            error_text = await response.text()
                            self._log_debug(
                                f"Server returned error: {response.status}, {error_text}"
                            )
                            return False, None, f"Сервер вернул код {response.status}"

            except Exception as e:
                if attempt < max_retries:
                    self._log_debug(
                        f"Request failed (attempt {attempt + 1}/{max_retries + 1}): {e}"
                    )
                    await asyncio.sleep(1)  # Короткая пауза перед повтором
                    continue
                else:
                    self._log_debug(
                        f"Error during API request after {max_retries + 1} attempts: {e}",
                        exc_info=True,
                    )
                    return False, None, str(e)

        return False, None, "Unexpected error in retry loop"


class ServerManager:
    """Управление локальным сервером FastAPI."""

    def __init__(self, debug: bool = False):
        """Инициализация менеджера сервера.

        Args:
            debug: Режим отладки
        """
        self.debug = debug
        self._log_debug(f"ServerManager initializing with debug mode: {self.debug}")
        self.server_process = None
        self.console = Console()
        self._log_debug("ServerManager initialized.")

    def _log_debug(self, message: str, exc_info: bool = False):
        """Логирование отладочных сообщений.

        Args:
            message: Сообщение для логирования
            exc_info: Включать ли информацию об исключении
        """
        if self.debug:
            logger.debug(message, exc_info=exc_info)

    async def start_server(self, api_client: ApiClient):
        """Запуск FastAPI сервера, если он еще не запущен.

        Args:
            api_client: Клиент API для проверки доступности сервера
        """
        self._log_debug(
            "Attempting to start local server instance (if not already running)."
        )

        # Проверяем, доступен ли уже сервер
        if await api_client.check_server_available():
            self.console.print("[green]Сервер уже запущен и доступен[/green]")
            self._log_debug("Server already running and available.")
            return

        try:
            # Запуск сервера в отдельном процессе
            server_script = Path(__file__).parent.parent / "main.py"
            self._log_debug(f"Starting server script: {server_script}")
            self.server_process = subprocess.Popen(
                [sys.executable, str(server_script)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )
            self._log_debug(
                f"Server process started with PID: {self.server_process.pid if self.server_process else 'N/A'}"
            )
            # Ждем немного, чтобы сервер успел запуститься
            await asyncio.sleep(3)
        except Exception as e:
            self.console.print(f"[red]Ошибка при запуске сервера: {e}[/red]")
            self._log_debug(f"Error during server startup: {e}", exc_info=True)
            sys.exit(1)

    def cleanup(self):
        """Очистка ресурсов при завершении."""
        self._log_debug("Cleanup called.")
        if self.server_process:
            self._log_debug(
                f"Terminating server process with PID: {self.server_process.pid}"
            )
            self.server_process.terminate()
            self.server_process.wait()
            self._log_debug("Server process terminated.")


class TtmlFileProcessor:
    """Обработчик TTML файлов."""

    def __init__(self, directory: str, api_client: ApiClient, debug: bool = False):
        """Инициализация обработчика файлов.

        Args:
            directory: Директория с TTML файлами
            api_client: Клиент API для конвертации
            debug: Режим отладки
        """
        self.debug = debug
        self._log_debug(f"TtmlFileProcessor initializing with debug mode: {self.debug}")

        self.directory = Path(directory).resolve()
        self.api_client = api_client
        self.console = Console()
        self._log_debug(f"TtmlFileProcessor initialized. Directory: {self.directory}")

    def _log_debug(self, message: str, exc_info: bool = False):
        """Логирование отладочных сообщений.

        Args:
            message: Сообщение для логирования
            exc_info: Включать ли информацию об исключении
        """
        if self.debug:
            logger.debug(message, exc_info=exc_info)

    async def process_file(
        self, file_path: Path, progress: Progress, task_id: int
    ) -> bool:
        """Обработка одного TTML файла.

        Args:
            file_path: Путь к TTML файлу
            progress: Объект для отображения прогресса
            task_id: ID задачи в прогрессе

        Returns:
            bool: True, если файл успешно обработан, иначе False
        """
        self._log_debug(f"Processing file: {file_path}")
        try:
            base_name = file_path.stem
            txt_path = file_path.parent / f"{base_name}.txt"

            if txt_path.exists():
                progress.update(
                    task_id,
                    description=f"[yellow]Пропущен (TXT существует): {file_path.name}[/yellow]",
                )
                return False

            ttml_content = file_path.read_text(encoding="utf-8")
            if not ttml_content.strip():
                progress.update(
                    task_id,
                    description=f"[red]Пропущен (пустой файл): {file_path.name}[/red]",
                )
                return False

            # Отправляем запрос на конвертацию через API клиент
            progress.update(
                task_id,
                description=f"Отправка на сервер: {file_path.name}",
            )

            (
                success,
                txt_content,
                error_message,
            ) = await self.api_client.convert_ttml_to_txt(ttml_content)

            if not success:
                self._log_debug(
                    f"Conversion failed for {file_path.name}: {error_message}"
                )
                progress.update(
                    task_id,
                    description=f"[red]Ошибка ({file_path.name}): {error_message}[/red]",
                )
                return False

            # Сохраняем очищенный текст в TXT файл
            txt_path.write_text(txt_content, encoding="utf-8")

            # Копируем временные метки из исходного файла
            import os

            source_stat = os.stat(file_path)
            os.utime(
                txt_path,
                (
                    source_stat.st_atime,
                    source_stat.st_mtime,
                ),
            )

            self._log_debug(
                f"Conversion successful for {file_path.name}, saved to {txt_path}"
            )
            progress.update(
                task_id,
                description=f"[green]Обработан: {file_path.name}[/green]",
            )
            return True
        except Exception as e:
            self._log_debug(
                f"Unhandled error in process_file for {file_path.name}: {e}",
                exc_info=True,
            )
            progress.update(
                task_id, description=f"[red]Ошибка ({file_path.name}): {str(e)}[/red]"
            )
            return False

    async def process_directory(self, max_concurrent: int = 10):
        """Обработка всех TTML файлов в директории с параллельной обработкой.

        Args:
            max_concurrent: Максимальное количество одновременных запросов
        """
        self._log_debug(f"Scanning directory: {self.directory} for .ttml files.")
        if not self.directory.exists() or not self.directory.is_dir():
            self.console.print(
                f"[red]Ошибка: Директория {self.directory} не существует[/red]"
            )
            self._log_debug(
                f"Directory {self.directory} does not exist or is not a directory."
            )
            return

        ttml_files = list(self.directory.glob("**/*.ttml"))
        self._log_debug(f"Found {len(ttml_files)} .ttml files to process.")
        if not ttml_files:
            self.console.print("[yellow]Предупреждение: TTML файлы не найдены[/yellow]")
            return

        self.console.print(
            f"[blue]Обработка {len(ttml_files)} файлов с {max_concurrent} одновременными запросами[/blue]"
        )

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console,
        ) as progress:
            # Добавляем общий прогресс
            main_task = progress.add_task(
                "[blue]Обработка файлов...[/blue]", total=len(ttml_files)
            )

            # Создаем семафор для ограничения количества одновременных запросов
            semaphore = asyncio.Semaphore(max_concurrent)
            completed = 0

            async def process_with_semaphore(ttml_file: Path) -> bool:
                """Обработка файла с семафором для ограничения параллелизма."""
                async with semaphore:
                    file_task = progress.add_task(
                        f"Подготовка: {ttml_file.name}", total=None
                    )
                    try:
                        result = await self.process_file(ttml_file, progress, file_task)
                        progress.update(main_task, advance=1)
                        return result
                    finally:
                        progress.remove_task(file_task)

            # Обрабатываем файлы параллельно батчами
            batch_size = max_concurrent * 2  # Обрабатываем по 2 батча за раз

            for i in range(0, len(ttml_files), batch_size):
                batch = ttml_files[i : i + batch_size]
                self._log_debug(
                    f"Processing batch {i // batch_size + 1}: {len(batch)} files"
                )

                # Запускаем обработку батча параллельно
                tasks = [process_with_semaphore(ttml_file) for ttml_file in batch]
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # Подсчитываем успешные обработки
                for result in results:
                    if isinstance(result, bool) and result:
                        completed += 1
                    elif isinstance(result, Exception):
                        self._log_debug(f"Exception in batch processing: {result}")

                # Небольшая пауза между батчами для снижения нагрузки на сервер
                if i + batch_size < len(ttml_files):
                    await asyncio.sleep(0.1)

            # Выводим итоговую статистику
            self.console.print("\n[green]Обработка завершена![/green]")
            self.console.print(f"Всего файлов: {len(ttml_files)}")
            self.console.print(f"Успешно обработано: {completed}")
            self.console.print(f"Пропущено/Ошибок: {len(ttml_files) - completed}")
            self.console.print(f"Использовано одновременных запросов: {max_concurrent}")


async def main():
    # Настраиваем парсер аргументов командной строки
    parser = argparse.ArgumentParser(description="Конвертация TTML файлов в TXT")
    parser.add_argument("directory", help="Директория с TTML файлами")
    parser.add_argument(
        "--host",
        default=DEFAULT_HOST,
        help=f"Хост сервера (по умолчанию: {DEFAULT_HOST})",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=DEFAULT_PORT,
        help=f"Порт сервера (по умолчанию: {DEFAULT_PORT})",
    )
    parser.add_argument(
        "--api-token",
        default=TEST_API_TOKEN,
        help="API токен для авторизации запросов к серверу",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable detailed debug logging on the client side.",
    )
    parser.add_argument(
        "--concurrent",
        type=int,
        default=1,
        help="Maximum number of concurrent requests (default: 10)",
    )

    args = parser.parse_args()

    # Configure logging level based on --debug flag
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    logger.debug("Debug mode enabled by command line flag.")

    try:
        # Создаем клиент API
        api_client = ApiClient(
            args.host,
            args.port,
            api_token=args.api_token,
            debug=args.debug,
        )

        # Создаем менеджер сервера
        server_manager = ServerManager(debug=args.debug)

        # Запускаем сервер, если он не запущен
        await server_manager.start_server(api_client)

        # Создаем обработчик файлов
        file_processor = TtmlFileProcessor(
            args.directory,
            api_client,
            debug=args.debug,
        )

        # Обрабатываем файлы
        await file_processor.process_directory(max_concurrent=args.concurrent)

    except KeyboardInterrupt:
        logger.info("\nПрерывание работы...")  # Use logger
    finally:
        if "server_manager" in locals() and hasattr(server_manager, "cleanup"):
            server_manager.cleanup()


if __name__ == "__main__":
    # BasicConfig is set in main() after parsing args now.
    asyncio.run(main())
